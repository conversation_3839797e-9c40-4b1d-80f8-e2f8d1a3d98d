import React, { useState } from 'react';
import LocationPickerModal from './LocationPickerModal';

const LocationPicker = ({ value, onChange, placeholder = "Select location on map", mode = "edit" }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleLocationSelect = (location) => {
    // Ensure coordinates are numbers
    const processedLocation = {
      ...location,
      coordinates: location.coordinates ? {
        lat: Number(location.coordinates.lat),
        lng: Number(location.coordinates.lng)
      } : null
    };
    
    console.log('📍 Location Data to be sent in request:', {
      Address: processedLocation.address,
      latitude: processedLocation.coordinates?.lat,
      longitude: processedLocation.coordinates?.lng,
      formatted: processedLocation.formatted
    });
    onChange(processedLocation);
  };

  const openModal = () => {
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  return (
    <div className="location-picker-field">
      {value && value.address ? (
        <div className="location-display-container">
          <div className="selected-location-display">
            <div className="location-details">
              <div className="location-address">{value.address}</div>
              <div className="location-coordinates">
                {value.coordinates?.lat ? Number(value.coordinates.lat).toFixed(6) : 'N/A'}, {value.coordinates?.lng ? Number(value.coordinates.lng).toFixed(6) : 'N/A'}
              </div>
            </div>
            <button
              type="button"
              className="change-location-btn"
              onClick={openModal}
            >
              <i className="pi pi-map-marker"></i>
              Change Location
            </button>
          </div>
        </div>
      ) : (
       
          <button
            type="button"
            className="select-location-btn"
            onClick={openModal}
          >
            <i className="pi pi-map-marker"></i>
            Select Company Location on Map
          </button>
          
       
      )}

      {isModalOpen && (
        <LocationPickerModal
          isOpen={isModalOpen}
          onClose={closeModal}
          onLocationSelect={handleLocationSelect}
          initialLocation={value}
          mode={mode}
        />
      )}
    </div>
  );
};

export default LocationPicker;
